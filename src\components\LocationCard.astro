---
export interface Location {
  name: string;
  slug: string;
  children?: Location[];
}

export interface Props {
  location: Location;
  href: string;
  restAreaCount?: number;
}

const { location, href, restAreaCount } = Astro.props;
---

<a href={href} class="block">
  <article class="card group hover:scale-105 transition-transform duration-300">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xl font-semibold text-secondary-900 dark:text-secondary-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
          {location.name}
        </h3>

      <svg
        class="w-5 h-5 text-secondary-400 dark:text-secondary-600 group-hover:text-primary-500 dark:group-hover:text-primary-400 transition-colors"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </div>

    <div class="space-y-2">
      {location.children && location.children.length > 0 && (
        <p class="text-sm text-secondary-600 dark:text-secondary-400">
          {location.children.length} sub-location{location.children.length !== 1 ? 's' : ''}
        </p>
      )}

      {restAreaCount !== undefined && (
        <p class="text-sm text-secondary-600 dark:text-secondary-400">
          {restAreaCount} rest area{restAreaCount !== 1 ? 's' : ''}
        </p>
      )}

      <div class="flex items-center text-primary-600 dark:text-primary-400 text-sm font-medium">
        <span>Explore location</span>
        <svg
          class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M14 5l7 7m0 0l-7 7m7-7H3"
          />
        </svg>
      </div>
    </div>
  </div>
  </article>
</a>
