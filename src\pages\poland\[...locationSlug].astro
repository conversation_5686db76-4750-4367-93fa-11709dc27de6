---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import RestAreaCard from '../../components/RestAreaCard.astro';
import LocationCard from '../../components/LocationCard.astro';
import Breadcrumbs from '../../components/Breadcrumbs.astro';
import locationsData from '../../data/locations_pl.json';
import { generateLocationStructuredData, generateBreadcrumbStructuredData } from '../../utils/structuredData.ts';
import { getLangFromUrl, useTranslations } from '../../i18n/utils';

export async function getStaticPaths() {
  const paths: any[] = [];

  // Sort locations alphabetically by name for consistent path generation
  const sortedLocationsData = [...locationsData].sort((a, b) => a.name.localeCompare(b.name));

  // Generate paths for all locations
  sortedLocationsData.forEach((region: any) => {
    // Add region path
    paths.push({
      params: { locationSlug: region.slug },
      props: {
        currentLocation: region,
        locationPath: [region],
        isLeaf: region.children.length === 0
      }
    });

    // Add branch paths (sort children alphabetically)
    [...region.children].sort((a: any, b: any) => a.name.localeCompare(b.name)).forEach((branch: any) => {
      paths.push({
        params: { locationSlug: `${region.slug}/${branch.slug}` },
        props: {
          currentLocation: branch,
          locationPath: [region, branch],
          isLeaf: true
        }
      });
    });
  });

  return paths;
}

const { currentLocation, locationPath, isLeaf } = Astro.props;
const { locationSlug } = Astro.params;

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Get English rest area entries. IDs are like 'en/some-area.md'.
const englishRestAreas = await getCollection('rest-areas', ({ id }) => id.startsWith('en/'));

// Filter rest areas for current location
const locationRestAreas = englishRestAreas.filter((area) => {
  const areaLocationPath = area.data.location_path;
  return areaLocationPath.includes(currentLocation.slug);
});

// Build breadcrumbs
const breadcrumbs = [
  { label: t('nav.home'), href: '/' },
  { label: t('countries.PL'), href: '/poland/' },
  ...locationPath.map((loc: any, index: number) => ({
    label: loc.name,
    href: index === 0 ? `/poland/${loc.slug}/` : `/poland/${locationPath[0].slug}/${loc.slug}/`
  }))
];

const pageTitle = `Rest Stops in ${currentLocation.name}, Poland`;
const pageDescription = `Find highway rest areas in ${currentLocation.name}, Poland. Browse ${locationRestAreas.length} rest stops with detailed amenities and information.`;

// Generate structured data
const locationUrl = new URL(Astro.url.pathname, Astro.site).href;
const parentLocation = locationPath.length > 1 ? locationPath[0] : undefined;
const locationStructuredData = generateLocationStructuredData(
  currentLocation,
  locationUrl,
  locationRestAreas.length,
  parentLocation
);
const breadcrumbStructuredData = generateBreadcrumbStructuredData(breadcrumbs);

// Combine structured data
const combinedStructuredData = [locationStructuredData, breadcrumbStructuredData];
---

<Layout
  title={pageTitle}
  description={pageDescription}
  type="place"
  structuredData={combinedStructuredData}
>
  <main>
    <div class="container-custom pt-8">
      <Breadcrumbs items={breadcrumbs} />

      <div class="mb-8">
        <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
          Rest Stops in {currentLocation.name}, Poland
        </h1>
        <p class="text-xl text-secondary-600 dark:text-secondary-300">
          {isLeaf ? (
            `Discover ${locationRestAreas.length} highway rest areas in ${currentLocation.name}`
          ) : (
            `Browse locations and rest areas in ${currentLocation.name} region`
          )}
        </p>
      </div>

      {!isLeaf && currentLocation.children && currentLocation.children.length > 0 ? (
        <!-- Show child locations -->
        <section class="mb-16">
          <h2 class="text-2xl font-semibold mb-6">Locations in {currentLocation.name}</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...currentLocation.children].sort((a: any, b: any) => a.name.localeCompare(b.name)).map((child: any) => {
              const childRestAreas = englishRestAreas.filter((area: any) =>
                area.data.location_path.includes(child.slug)
              );
              return (
                <LocationCard
                  location={child}
                  href={`/poland/${currentLocation.slug}/${child.slug}/`}
                  restAreaCount={childRestAreas.length}
                />
              );
            })}
          </div>
        </section>
      ) : null}

      {locationRestAreas.length > 0 ? (
        <!-- Show rest areas -->
        <section class="mb-16">
          <h2 class="text-2xl font-semibold mb-6">
            Rest Areas {isLeaf ? `in ${currentLocation.name}` : `in ${currentLocation.name} Region`}
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {locationRestAreas.map((restArea) => {
              // Let RestAreaCard determine its own link based on its restArea prop and current page locale
              return (
                <RestAreaCard
                  restArea={restArea}
                />
              );
            })}
          </div>
        </section>
      ) : isLeaf ? (
        <!-- No rest areas found -->
        <section class="text-center py-12 mb-16">
          <div class="max-w-md mx-auto">
            <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
              No Rest Areas Found
            </h2>
            <p class="text-secondary-600 dark:text-secondary-400 mb-6">
              We don't have any rest areas listed for {currentLocation.name} yet.
            </p>
            <a href="/poland/" class="btn-primary">
              Browse Other Locations
            </a>
          </div>
        </section>
      ) : null}
    </div>
  </main>
</Layout>
