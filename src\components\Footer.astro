---
const currentYear = new Date().getFullYear();

const footerLinks = [
  {
    title: "Browse",
    links: [
      { text: "Home", url: "/" },
      { text: "Browse Locations", url: "/pl/" },
      { text: "All Rest Areas", url: "/rest-areas/" },
      { text: "Popular Stops", url: "/#popular" }
    ]
  },
  {
    title: "Locations",
    links: [
      { text: "Poland", url: "/pl/" },
      { text: "Podlaskie", url: "/pl/podlaskie/" },
      { text: "Śląskie", url: "/pl/slaskie/" },
      { text: "Małopolskie", url: "/pl/malopolskie/" }
    ]
  },
  {
    title: "Information",
    links: [
      { text: "About stops24", url: "/about" },
      { text: "How to Use", url: "/help" },
      { text: "Contact Us", url: "/contact" }
    ]
  }
];
---

<footer class="bg-gray-900 text-white pt-16 pb-8">
  <div class="container-custom">
    <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
      <div class="md:col-span-2 lg:col-span-2">
        <a href="/" class="flex items-center mb-6" aria-label="Go to homepage">
          <!-- Highway Rest Area Logo SVG -->
          <svg class="h-8 w-auto text-primary-400" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <!-- Background circle -->
            <circle cx="50" cy="50" r="48" fill="currentColor" opacity="0.1" />

            <!-- Road -->
            <rect x="20" y="45" width="60" height="10" fill="currentColor" />
            <rect x="20" y="48" width="60" height="2" fill="#fbbf24" />
            <rect x="20" y="50" width="60" height="2" fill="#fbbf24" />

            <!-- Rest area building -->
            <rect x="35" y="25" width="30" height="20" fill="currentColor" opacity="0.8" />
            <polygon points="35,25 50,15 65,25" fill="currentColor" />

            <!-- Windows -->
            <rect x="40" y="30" width="4" height="6" fill="#1e293b" />
            <rect x="46" y="30" width="4" height="6" fill="#1e293b" />
            <rect x="52" y="30" width="4" height="6" fill="#1e293b" />
            <rect x="58" y="30" width="4" height="6" fill="#1e293b" />

            <!-- Door -->
            <rect x="47" y="38" width="6" height="7" fill="#475569" />

            <!-- Parking spaces -->
            <rect x="25" y="60" width="8" height="12" fill="none" stroke="currentColor" stroke-width="1" />
            <rect x="35" y="60" width="8" height="12" fill="none" stroke="currentColor" stroke-width="1" />
            <rect x="57" y="60" width="8" height="12" fill="none" stroke="currentColor" stroke-width="1" />
            <rect x="67" y="60" width="8" height="12" fill="none" stroke="currentColor" stroke-width="1" />
          </svg>
          <span class="ml-2 text-xl font-display font-semibold text-white">stops24</span>
        </a>
        <p class="text-gray-400 mb-6 max-w-md">
          Your comprehensive guide to highway rest areas across Europe. Find amenities, ratings, and detailed information for safe and comfortable travels.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors" aria-label="Twitter">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors" aria-label="LinkedIn">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors" aria-label="Facebook">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors" aria-label="GitHub">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"></path>
            </svg>
          </a>
        </div>
      </div>

      {footerLinks.map(column => (
        <div>
          <h3 class="text-lg font-semibold mb-4">{column.title}</h3>
          <ul class="space-y-2">
            {column.links.map(link => (
              <li>
                <a href={link.url} class="text-gray-400 hover:text-primary-400 transition-colors">{link.text}</a>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>

    <div class="border-t border-gray-800 pt-8 mt-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm mb-4 md:mb-0">
          &copy; {currentYear} stops24.com. All rights reserved.
        </p>
        <div class="flex space-x-6">
          <a href="/privacy-policy" class="text-gray-400 hover:text-primary-400 text-sm transition-colors">Privacy Policy</a>
          <a href="/terms-conditions" class="text-gray-400 hover:text-primary-400 text-sm transition-colors">Terms of Service</a>
        </div>
      </div>
    </div>
  </div>
</footer>
