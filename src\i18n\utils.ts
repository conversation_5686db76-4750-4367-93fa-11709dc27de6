import en from './en.json';
import pl from './pl.json';

export const languages = {
  en: 'English',
  pl: 'Polski'
};

export const defaultLang = 'en';
export const supportedLanguages = Object.keys(languages) as Array<keyof typeof languages>;

const translations = {
  en,
  pl
};

export type Language = keyof typeof translations;

export function getLangFromUrl(url: URL): Language {
  const [, lang] = url.pathname.split('/');
  if (lang in translations) return lang as Language;
  return defaultLang;
}

export function useTranslations(lang: Language) {
  return function t(key: string): string {
    const keys = key.split('.');
    let value: any = translations[lang];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to English if key not found
        value = translations[defaultLang];
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey];
          } else {
            console.warn(`Translation key "${key}" not found for language "${lang}"`);
            return key; // Return the key itself as fallback
          }
        }
        break;
      }
    }

    return typeof value === 'string' ? value : key;
  };
}

export function getLocalizedPath(path: string, lang: Language): string {
  // Remove leading slash and any existing language prefix
  let cleanPath = path.replace(/^\//, '').replace(/^(en|pl)\//, '');

  // Handle special case: Terms of Service page mapping
  if (cleanPath === 'terms-of-service' || cleanPath === 'terms-conditions') {
    if (lang === defaultLang) {
      return '/terms-conditions';
    } else {
      return `/${lang}/terms-of-service`;
    }
  }

  // Handle special case: Poland/Polska URL mapping
  if (cleanPath.startsWith('poland/') || cleanPath.startsWith('polska/') || path.includes('/poland/') || path.includes('/polska/')) {
    // Extract the location path after poland/polska
    let locationPath = '';
    if (cleanPath.startsWith('poland/')) {
      locationPath = cleanPath.replace('poland/', '');
    } else if (cleanPath.startsWith('polska/')) {
      locationPath = cleanPath.replace('polska/', '');
    } else if (path.includes('/poland/')) {
      locationPath = cleanPath.replace(/.*\/poland\//, '');
    } else if (path.includes('/polska/')) {
      locationPath = cleanPath.replace(/.*\/polska\//, '');
    }

    if (lang === defaultLang) {
      // English uses 'poland'
      return locationPath ? `/poland/${locationPath}` : '/poland/';
    } else {
      // Polish uses 'polska'
      return locationPath ? `/${lang}/polska/${locationPath}` : `/${lang}/polska/`;
    }
  }

  // For non-poland paths, handle normally
  if (lang === defaultLang) {
    return `/${cleanPath}`;
  }

  return `/${lang}/${cleanPath}`;
}

export function detectBrowserLanguage(): Language {
  if (typeof window === 'undefined') return defaultLang;

  // Check localStorage first
  const stored = localStorage.getItem('language');
  if (stored && stored in translations) {
    return stored as Language;
  }

  // Check browser language
  const browserLang = navigator.language.split('-')[0];
  if (browserLang in translations) {
    return browserLang as Language;
  }

  return defaultLang;
}

export function setLanguagePreference(lang: Language): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('language', lang);
}

// Helper function to get localized URL slugs
export function getLocalizedSlug(slug: string, lang: Language): string {
  const t = useTranslations(lang);

  // Try to get localized slug from translations
  const localizedSlug = t(`urlSlugs.${slug}`);
  if (localizedSlug !== `urlSlugs.${slug}`) {
    return localizedSlug;
  }

  // Fallback to original slug
  return slug;
}

// Helper function to get translated location names
export function getTranslatedLocationName(slug: string, lang: Language): string {
  const t = useTranslations(lang);

  // Try regions first, then cities
  const regionName = t(`regions.${slug}`);
  if (regionName !== `regions.${slug}`) {
    return regionName;
  }

  const cityName = t(`cities.${slug}`);
  if (cityName !== `cities.${slug}`) {
    return cityName;
  }

  // Fallback to original slug formatted
  return slug.split('-').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}
